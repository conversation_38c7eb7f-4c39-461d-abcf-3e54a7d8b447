<template>
    <div class="course-detail">
        <!-- 返回按钮 -->
        <BackButton title="考试详情" backPath="/smart-trainer/assess/home" />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载课程详情...</p>
        </div>

        <!-- 课程详情内容 -->
        <div v-else-if="examData" class="detail-content">
            <div class="core-info-section">
                <!-- Top part: Main course info -->
                <div class="course-main">
                    <div class="course-thumbnail-wrapper">
                        <div class="thumbnail-image">
                            <img :src="examData.imageUrl" alt="Course Thumbnail" />
                        </div>
                        <div class="new-badge">新课</div>
                    </div>
                    <div class="course-info">
                        <h1 class="title">{{ examData.name }}</h1>
                        <p class="instructor">{{ examData.typeLabel }}</p>
                        <!-- The following is static based on design, as no data is available -->
                        <div class="tags-section">
                            <span v-for="tag in examData.tags.slice(0, 3)" :key="tag" class="tag">
                                {{ tag }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Bottom part: Stats bar -->
                <div class="course-stats">
                    <div class="stat">
                        <div class="label">课程难度</div>
                        <div class="value">{{ examData.difficulty }}</div>
                    </div>
                    <div class="stat">
                        <div class="label">视频时长</div>
                        <div class="value">{{ examData.duration }}分钟</div>
                    </div>
                    <div class="stat">
                        <div class="label">学习人数</div>
                        <div class="value">{{ examData.participantCount }}</div>
                    </div>
                    <div class="stat">
                        <div class="label">课程评分</div>
                        <div class="value">10.00分</div>
                        <!-- Static from design -->
                    </div>
                </div>
            </div>

            <!-- 题目分布区 -->
            <div v-if="examData.questionTypes" class="question-distribution-section">
                <div class="distribution-tags">
                    <div
                        v-for="type in examData.questionTypes"
                        :key="type.type"
                        class="distribution-tag"
                        :class="`type-${type.type}`"
                    >
                        <span class="type-name">{{ getTypeName(type.type) }}</span>
                        <span class="type-count">{{ type.count }}题</span>
                    </div>
                </div>
            </div>

            <!-- 内容切换区 -->
            <div class="content-tabs-section">
                <div class="tabs-header">
                    <button
                        class="tab-button"
                        :class="{ active: activeTab === 'intro' }"
                        @click="activeTab = 'intro'"
                    >
                        简介
                    </button>
                    <button
                        class="tab-button"
                        :class="{ active: activeTab === 'ai-summary' }"
                        @click="activeTab = 'ai-summary'"
                    >
                        AI 总结
                    </button>
                </div>
            </div>

            <!-- 内容详情区 -->
            <div class="content-detail-section">
                <!-- 简介标签页内容 -->
                <div v-if="activeTab === 'intro'" class="intro-content">
                    <!-- 课程简介模块 -->
                    <div class="course-introduction">
                        <h3>课程简介</h3>
                        <div class="intro-content-text">
                            <p>{{ examData.description }}</p>
                            <p>
                                本课程采用理论与实践相结合的方式，通过系统性的学习和实战演练，帮助学员全面掌握核心知识点。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- AI总结标签页内容 -->
                <div v-else-if="activeTab === 'ai-summary'" class="ai-summary-content">
                    <div class="ai-summary-card">
                        <div class="ai-header">
                            <i class="pi pi-sparkles"></i>
                            <h3>AI 智能总结</h3>
                        </div>
                        <div class="ai-content">
                            <p>基于课程内容和学习目标，AI为您智能生成以下总结：</p>
                            <ul class="ai-points">
                                <li>本课程是{{ examData.typeLabel }}领域的核心培训内容</li>
                                <li>适合初学者和有一定基础的学员参与</li>
                                <li>
                                    通过{{ examData.questionCount }}道精心设计的题目检验学习效果
                                </li>
                                <li>预计学习时长{{ examData.duration }}分钟，建议集中时间完成</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 课程不存在 -->
        <div v-else class="not-found">
            <i class="pi pi-exclamation-triangle"></i>
            <h3>课程不存在</h3>
            <p>您访问的课程可能已被删除或不存在</p>
            <button class="back-btn" @click="goBack">
                <i class="pi pi-arrow-left"></i>
                <span>返回列表</span>
            </button>
        </div>

        <!-- 底部操作栏 -->
        <div v-if="examData" class="bottom-action-bar">
            <button
                class="action-btn purchase-btn"
                @click="startExam"
                :disabled="examData.status !== 'available'"
            >
                <i class="pi pi-play"></i>
                <span>{{ getActionText() }}</span>
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BackButton from '@/views/smart-trainer/components/BackButton.vue';

// 路由
const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const examData = ref(null);
const activeTab = ref('intro');

// Mock 考试数据（实际应该从API获取）
const mockExamData = {
    exam_001: {
        id: 'exam_001',
        name: '廉洁合规测试',
        description:
            '本测试包含单选题、多选题和判断题，请仔细阅读题目后作答。测试内容涵盖公司廉洁合规相关制度、利益冲突管理、礼品礼金处理等重要内容。',
        type: 'compliance',
        typeLabel: '廉洁合规',
        departmentId: 1,
        typeId: 1,
        duration: 30,
        questionCount: 15,
        difficulty: '中等',
        status: 'available',
        icon: 'pi-shield',
        imageUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/default-avatar.png',
        tags: ['廉洁', '合规', '必修'],
        participantCount: 1248,
        questionTypes: [
            { type: 'single', count: 5 },
            { type: 'multi', count: 4 },
            { type: 'true_false', count: 6 }
        ]
    },
    exam_002: {
        id: 'exam_002',
        name: '汽车销售技能考核',
        description:
            '测试汽车销售相关的专业知识和技能掌握情况，包括产品知识、销售技巧、客户服务等方面的内容。',
        type: 'skill',
        typeLabel: '业务技能',
        departmentId: 2,
        typeId: 2,
        duration: 45,
        questionCount: 20,
        difficulty: '困难',
        status: 'available',
        icon: 'pi-car',
        imageUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/default-avatar.png',
        tags: ['销售', '技能', '专业'],
        participantCount: 856,
        questionTypes: [
            { type: 'single', count: 8 },
            { type: 'multi', count: 7 },
            { type: 'true_false', count: 5 }
        ]
    }
};

/**
 * 组件挂载时加载考试数据
 */
onMounted(() => {
    loadExamData();
});

/**
 * 加载考试详情数据
 */
const loadExamData = async () => {
    try {
        loading.value = true;
        const examId = route.params.examId;

        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 800));

        // 获取考试数据
        examData.value = mockExamData[examId] || null;
    } catch (error) {
        console.error('加载考试详情失败:', error);
        examData.value = null;
    } finally {
        loading.value = false;
    }
};

/**
 * 获取题目类型名称
 * @param {string} type - 题目类型
 */
const getTypeName = type => {
    const typeMap = {
        single: '单选题',
        multi: '多选题',
        true_false: '判断题'
    };
    return typeMap[type] || '未知类型';
};

/**
 * 获取操作按钮文本
 */
const getActionText = () => {
    if (!examData.value) {
        return '开始考试';
    }

    switch (examData.value.status) {
        case 'available':
            return '开始考试';
        case 'completed':
            return '重新考试';
        case 'locked':
            return '暂未开放';
        default:
            return '开始考试';
    }
};

/**
 * 返回列表页
 */
const goBack = () => {
    router.push('/smart-trainer/assess/list');
};

/**
 * 开始考试
 */
const startExam = () => {
    if (examData.value && examData.value.status === 'available') {
        router.push(`/smart-trainer/assess/quiz/${examData.value.id}`);
    }
};

/**
 * 咨询课程
 */
const consultCourse = () => {
    // 实现咨询功能
    console.log('咨询课程');
};

/**
 * 试看课程
 */
const previewCourse = () => {
    // 实现试看功能
    console.log('试看课程');
};
</script>

<style lang="scss" scoped>
// 配色方案变量
$primary-white: #ffffff;
$primary-text: #333333;
$secondary-text: #666666;
$light-text: #999999;
$primary-blue: #1890ff;
$light-blue: #e6f7ff;
$light-gray: #f5f5f5;
$border-gray: #e8e8e8;
$success-green: #52c41a;

.course-detail {
    width: 100%;
    height: 100%;
    background: $primary-white;
    padding-bottom: 80px; // 为底部操作栏留出空间
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: $secondary-text;

    .loading-spinner {
        width: 48px;
        height: 48px;
        border: 4px solid $light-gray;
        border-top: 4px solid $primary-blue;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 24px;
    }

    p {
        font-size: 16px;
        font-weight: 500;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
}

.detail-content {
    max-width: 100%;
    margin: 0 auto;
}

// 核心信息区
.core-info-section {
    padding: 16px;
    background: $primary-white;
}

.course-main {
    display: flex;
    gap: 16px;
    margin-bottom: 14px;
}

.course-thumbnail-wrapper {
    position: relative;
    flex-shrink: 0;
    width: 120px;
    height: 90px;

    .thumbnail-image {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        background: #f0f2f5; // Placeholder background
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .new-badge {
        position: absolute;
        top: -4px;
        left: -4px;
        background-color: #ff7d3a;
        color: white;
        padding: 4px 10px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 8px 0 8px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}

.course-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    min-width: 0;

    .title {
        font-size: 18px;
        font-weight: 700;
        color: $primary-text;
        line-height: 1.4;
        margin: 0 0 4px 0;
    }

    .instructor {
        font-size: 13px;
        color: $secondary-text;
        margin: 0 0 8px 0;
    }

    .tags-section {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    .tag {
        font-size: 11px;
        padding: 4px 10px;
        border-radius: 6px;
        font-weight: 500;
        border: none;
        transition: all 0.2s ease;

        // 根据不同的标签内容给予不同的现代化颜色
        &:nth-child(1) {
            background: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);
            color: #667eea;
            border: 1px solid #667eea30;
        }

        &:nth-child(2) {
            background: linear-gradient(135deg, #f093fb20 0%, #f5576c20 100%);
            color: #f5576c;
            border: 1px solid #f5576c30;
        }

        &:nth-child(3) {
            background: linear-gradient(135deg, #4facfe20 0%, #00f2fe20 100%);
            color: #4facfe;
            border: 1px solid #4facfe30;
        }

        // 如果有更多标签，使用备用颜色
        &:nth-child(n + 4) {
            background: linear-gradient(135deg, #a8edea20 0%, #fed6e320 100%);
            color: #20bf6b;
            border: 1px solid #20bf6b30;
        }

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.sale {
            background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
            color: white;
            border: none;
        }

        &.refund {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            gap: 4px;
        }
    }
}

.course-stats {
    display: flex;
    justify-content: space-around;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;

    .stat {
        text-align: center;

        .label {
            font-size: 13px;
            color: $secondary-text;
            margin-bottom: 4px;
        }

        .value {
            font-size: 15px;
            font-weight: 600;
            color: $primary-text;
        }
    }
}

// 题目分布区
.question-distribution-section {
    padding: 0 16px 16px 16px;

    .distribution-tags {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        align-items: center;

        .distribution-tag {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.3s ease;

            .type-name {
                opacity: 0.9;
            }

            .type-count {
                background: rgba(255, 255, 255, 0.9);
                color: inherit;
                padding: 1px 6px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 10px;
                min-width: 16px;
                text-align: center;
            }

            // 单选题 - 渐变蓝色
            &.type-single {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;

                .type-count {
                    color: #667eea;
                }
            }

            // 多选题 - 渐变绿色
            &.type-multi {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;

                .type-count {
                    color: #f5576c;
                }
            }

            // 判断题 - 渐变橙色
            &.type-true_false {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;

                .type-count {
                    color: #4facfe;
                }
            }

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
        }
    }
}

// 内容切换区
.content-tabs-section {
    padding: 0 24px;
    border-bottom: 1px solid $border-gray;

    .tabs-header {
        display: flex;
        gap: 32px;

        .tab-button {
            padding: 10px 0;
            background: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            color: $secondary-text;
            cursor: pointer;
            position: relative;
            transition: color 0.3s ease;

            &.active {
                color: $primary-blue;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background: $primary-blue;
                }
            }

            &:hover {
                color: $primary-blue;
            }
        }
    }
}

// 内容详情区
.content-detail-section {
    padding: 24px;
}

.intro-content {
    .course-introduction {
        h3 {
            font-size: 18px;
            font-weight: 600;
            color: $primary-text;
            margin-bottom: 16px;
        }

        .intro-content-text {
            color: $primary-text;
            line-height: 1.6;

            p {
                margin-bottom: 16px;
                font-size: 14px;
            }
        }
    }
}

.ai-summary-content {
    .ai-summary-card {
        .ai-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;

            i {
                font-size: 20px;
                color: $primary-blue;
            }

            h3 {
                font-size: 18px;
                font-weight: 600;
                color: $primary-text;
                margin: 0;
            }
        }

        .ai-content {
            color: $primary-text;

            p {
                font-size: 14px;
                line-height: 1.6;
                margin-bottom: 16px;
            }

            .ai-points {
                list-style: none;
                padding: 0;
                margin: 0;

                li {
                    padding: 8px 0;
                    font-size: 14px;
                    position: relative;
                    padding-left: 20px;

                    &::before {
                        content: '•';
                        color: $primary-blue;
                        font-weight: 600;
                        position: absolute;
                        left: 0;
                    }
                }
            }
        }
    }
}

// 底部操作栏
.bottom-action-bar {
    position: fixed;
    bottom: 60px;
    left: 0;
    right: 0;
    background: $primary-white;
    border-top: 1px solid $border-gray;
    padding: 16px 24px;
    display: flex;
    gap: 12px;
    z-index: 100;

    .action-btn {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        border: none;
        transition: all 0.3s ease;

        i {
            font-size: 16px;
        }

        &.consultation-btn {
            background: $light-gray;
            color: $secondary-text;

            &:hover {
                background: $border-gray;
            }
        }

        &.preview-btn {
            background: $light-blue;
            color: $primary-blue;

            &:hover {
                background: #d6f0ff;
            }
        }

        &.purchase-btn {
            background: $primary-blue;
            color: $primary-white;
            flex: 2;

            &:hover:not(:disabled) {
                background: #1976f2;
            }

            &:disabled {
                background: $light-text;
                cursor: not-allowed;
            }
        }
    }
}

.not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 80vh;
    color: $secondary-text;
    text-align: center;

    i {
        font-size: 64px;
        margin-bottom: 20px;
        color: #ffc107;
    }

    h3 {
        font-size: 24px;
        font-weight: 700;
        color: $primary-text;
        margin-bottom: 8px;
    }

    p {
        font-size: 14px;
        margin-bottom: 24px;
        line-height: 1.6;
    }

    .back-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: $primary-blue;
        color: $primary-white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #1976f2;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .course-header {
        flex-direction: column;
        gap: 16px;

        .course-thumbnail .thumbnail-image {
            width: 100%;
            height: 120px;
        }
    }

    .key-indicators {
        grid-template-columns: repeat(2, 1fr);
    }

    .content-tabs-section .tabs-header {
        gap: 24px;
    }

    .bottom-action-bar {
        flex-direction: column;
        gap: 8px;

        .action-btn {
            flex: none;
        }
    }
}

@media (max-width: 480px) {
    .core-info-section,
    .content-detail-section {
        padding: 16px;
    }

    .content-tabs-section {
        padding: 0 16px;
    }

    .key-indicators .indicator-item {
        min-width: 70px;
        padding: 8px;
    }
}
</style>
